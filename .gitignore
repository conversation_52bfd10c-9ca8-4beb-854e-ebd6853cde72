# If you need to exclude files such as those generated by an IDE, use
# $GIT_DIR/info/exclude or the mm.excludesFile configuration variable as
# described in https://git-scm.com/docs/gitignore

*.egg-info
*.pot
*.py[co]
.tox/
__pycache__
MANIFEST
dist/
docs/_build/
docs/locale/
node_modules/
tests/coverage_html/
tests/.coverage
build/
tests/report/
venv
.idea
log/
*.c
main.spec
build/
config.ini
ntwork/wc/*.pyd
ntwork/wc/*.dat
wheelhouse/
setup_conf.py
upload.bat