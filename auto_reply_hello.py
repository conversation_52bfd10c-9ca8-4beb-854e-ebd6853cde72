# -*- coding: utf-8 -*-
"""
自动回复机器人 - 收到消息后自动回复"你好啊"
基于ntwork企业微信API
"""
import sys
import time
import ntwork

def main():
    # 创建企业微信实例
    wework = ntwork.WeWork()
    
    print("正在启动企业微信...")
    # 打开pc企业微信, smart: 是否管理已经登录的企业微信
    wework.open(smart=True)
    
    print("等待登录...")
    # 等待登录
    wework.wait_login()
    
    # 获取登录信息
    login_info = wework.get_login_info()
    print(f"登录成功！当前用户: {login_info.get('name', '未知用户')}")
    
    # 注册消息回调 - 使用装饰器方式
    @wework.msg_register(ntwork.MT_RECV_TEXT_MSG)
    def on_recv_text_msg(wework_instance: ntwork.WeWork, message):
        try:
            data = message["data"]
            sender_user_id = data["sender"]
            self_user_id = wework_instance.get_login_info()["user_id"]
            conversation_id: str = data["conversation_id"]
            content = data.get("content", "")
            
            print(f"收到消息: {content}")
            print(f"发送者: {sender_user_id}")
            print(f"会话ID: {conversation_id}")
            
            # 判断消息不是自己发的
            if sender_user_id != self_user_id:
                # 判断是否为群消息 (群消息的conversation_id以"R:"开头)
                if conversation_id.startswith("R:"):
                    print("这是群消息，跳过自动回复")
                else:
                    print("这是私聊消息，准备自动回复...")
                    # 发送自动回复
                    wework_instance.send_text(
                        conversation_id=conversation_id, 
                        content="你好啊"
                    )
                    print("已自动回复: 你好啊")
            else:
                print("这是自己发送的消息，跳过")
                
            print("-" * 50)
            
        except Exception as e:
            print(f"处理消息时出错: {e}")
    
    print("自动回复机器人已启动！")
    print("功能说明:")
    print("- 只对私聊消息自动回复")
    print("- 不会回复群消息")
    print("- 不会回复自己发送的消息")
    print("- 收到消息后会自动回复'你好啊'")
    print("按 Ctrl+C 退出程序")
    print("-" * 50)
    
    # 保持程序运行
    try:
        while True:
            time.sleep(0.5)
    except KeyboardInterrupt:
        print("\n正在退出程序...")
        ntwork.exit_()
        sys.exit()

if __name__ == "__main__":
    main()
